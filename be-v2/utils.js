const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');

// 常量
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
const TX_TIMEOUT = 15000;
const MAX_PROPOSALS_TO_CHECK = 20;

// 存储已知的多签账户地址
let knownMultisigAddresses = [
  'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr', // 测试确认EMXYq4vz4QEyxj63gerfCGjxGBrBFc7gfoiNdpf85RiG是成员
  '6kCwpkN8qj7YZQpNdQMuMX8Qgf9GhWk5G6w2yx8MeHSS',
  'DxMbvVTVGzBm7YKNwSWAW89ZZ9bjnQnGJdAUP5PUQYt3',
  '5bYReP8iw5UYpAMHjAr8XQbLYHPZxYNYTZzs3yJNHJcT'
];

// 工具函数
const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key]);
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null;
};

const handleError = (ctx, error, message) => {
  const status = error.status || 500;
  ctx.status = status;
  ctx.body = { error: message || error.message };
};

const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    { skipPreflight: true, preflightCommitment: 'confirmed', maxRetries: 3, ...options }
  );
  return signature;
};

const confirmTransactionWithTimeout = async (signature, timeout = TX_TIMEOUT) => {
  try {
    const result = await Promise.race([
      connection.confirmTransaction(signature, 'confirmed'),
      new Promise((_, reject) => setTimeout(() => reject(new Error('确认超时')), timeout))
    ]);
    return { confirmed: true, result };
  } catch (error) {
    return { confirmed: false, error: error.message };
  }
};

// 获取与公钥相关的多签账户
const getMultisigAccountsForPublicKey = async (publicKey) => {
  try {
    const userPubkey = new PublicKey(publicKey);

    // 由于getProgramAccounts可能被禁用，使用更具体的方法
    // 这里使用一个已知的多签账户列表
    // 在生产环境中，应该使用数据库或索引服务来存储和查询多签账户

    const userMultisigs = [];

    // 并行检查每个已知的多签账户
    const checkPromises = knownMultisigAddresses.map(async (address) => {
      try {
        const multisigPubkey = new PublicKey(address);
        const multisigData = await multisig.accounts.Multisig.fromAccountAddress(
          connection,
          multisigPubkey
        );

        // 检查用户是否是成员
        const isMember = multisigData.members.some(member =>
          member.key.equals(userPubkey)
        );

        if (isMember) {
          return {
            address,
            members: multisigData.members.length,
            threshold: multisigData.threshold,
            transactionIndex: Number(multisigData.transactionIndex.toString()),
          };
        }
        return null;
      } catch (err) {
        // 跳过无效账户
        return null;
      }
    });

    const results = await Promise.all(checkPromises);
    userMultisigs.push(...results.filter(result => result !== null));

    return userMultisigs;
  } catch (error) {
    throw new Error(`获取多签账户失败: ${error.message}`);
  }
};

// 更新已知的多签地址列表
const updateKnownMultisigAddresses = (addresses) => {
  if (!Array.isArray(addresses)) {
    throw new Error('地址必须是数组格式');
  }

  // 验证地址格式
  addresses.forEach(address => {
    try {
      new PublicKey(address);
    } catch (err) {
      throw new Error(`无效的地址格式: ${address}`);
    }
  });

  // 更新地址列表，去重
  const uniqueAddresses = [...new Set([...knownMultisigAddresses, ...addresses])];
  knownMultisigAddresses = uniqueAddresses;

  return knownMultisigAddresses;
};

// 获取当前已知的多签地址列表
const getKnownMultisigAddresses = () => {
  return [...knownMultisigAddresses];
};

module.exports = {
  MULTISIG_PROGRAM_ID,
  connection,
  TX_TIMEOUT,
  MAX_PROPOSALS_TO_CHECK,
  validateParams,
  handleError,
  sendTransaction,
  confirmTransactionWithTimeout,
  getMultisigAccountsForPublicKey,
  updateKnownMultisigAddresses,
  getKnownMultisigAddresses
};