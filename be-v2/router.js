const Router = require('koa-router');
const multisig = require('@sqds/multisig');
const { PublicKey } = require('@solana/web3.js');
const {
  MULTISIG_PROGRAM_ID,
  connection,
  MAX_PROPOSALS_TO_CHECK,
  validateParams,
  handleError,
  sendTransaction,
  confirmTransactionWithTimeout,
  getMultisigAccountsForPublicKey,
  updateKnownMultisigAddresses,
  getKnownMultisigAddresses
} = require('./utils');

const router = new Router();

// ==================== 系统接口 ====================

// 健康检查
router.get('/health', async (ctx) => {
  ctx.body = { status: 'ok', timestamp: new Date().toISOString(), network: 'mainnet-beta' };
});

// 获取区块哈希
router.get('/api/blockhash', async (ctx) => {
  try {
    const { blockhash } = await connection.getLatestBlockhash();
    ctx.body = { blockhash };
  } catch (error) {
    handleError(ctx, error, '获取区块哈希失败');
  }
});

// 更新已知的多签地址列表
router.post('/api/admin/update-multisig-list', async (ctx) => {
  try {
    const { addresses, adminKey } = ctx.request.body;

    // 简单的管理员验证，实际生产环境应使用更安全的认证方式
    const ADMIN_KEY = process.env.ADMIN_KEY || 'admin-key-for-testing';
    if (adminKey !== ADMIN_KEY) {
      return handleError(ctx, { status: 403 }, '无权限执行此操作');
    }

    const error = validateParams(ctx.request.body, ['addresses']);
    if (error) return handleError(ctx, { status: 400 }, error);

    if (!Array.isArray(addresses)) {
      return handleError(ctx, { status: 400 }, '地址必须是数组格式');
    }

    try {
      const updatedList = updateKnownMultisigAddresses(addresses);
      ctx.body = {
        success: true,
        message: `已更新多签地址列表，当前共有 ${updatedList.length} 个地址`,
        addresses: updatedList
      };
    } catch (err) {
      return handleError(ctx, { status: 400 }, err.message);
    }
  } catch (error) {
    handleError(ctx, error, '更新多签地址列表失败');
  }
});

// 获取当前已知的多签地址列表
router.get('/api/admin/multisig-list', async (ctx) => {
  try {
    const addresses = getKnownMultisigAddresses();
    ctx.body = {
      success: true,
      count: addresses.length,
      addresses
    };
  } catch (error) {
    handleError(ctx, error, '获取多签地址列表失败');
  }
});

// ==================== 账户信息接口 ====================

// 获取账户信息（合并多个账户相关接口，包括Token余额）
router.post('/api/account', async (ctx) => {
  try {
    const { publicKey, multisigAddress, tokenAccount } = ctx.request.body;

    if (!publicKey && !multisigAddress && !tokenAccount) {
      return handleError(ctx, { status: 400 }, '需要提供publicKey、multisigAddress或tokenAccount');
    }

    const result = {};

    // 获取用户相关的多签账户
    if (publicKey) {
      try {
        // 使用新函数获取与用户相关的多签账户
        const multisigs = await getMultisigAccountsForPublicKey(publicKey);
        result.multisigs = multisigs;

        // 获取SOL余额
        try {
          const userPublicKey = new PublicKey(publicKey);
          const balance = await connection.getBalance(userPublicKey);
          result.balance = balance;
        } catch (err) {
          result.balance = 0;
        }
      } catch (error) {
        result.multisigs = [];
        result.balance = 0;
      }
    }

    // 获取指定多签的详细信息
    if (multisigAddress) {
      try {
        const multisigPubkey = new PublicKey(multisigAddress);
        const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

        // 获取金库信息
        const [vaultPda] = multisig.getVaultPda({
          multisigPda: multisigPubkey,
          index: 0,
          programId: MULTISIG_PROGRAM_ID
        });

        const vaultBalance = await connection.getBalance(vaultPda);

        result.multisig = {
          address: multisigAddress,
          members: multisigData.members.map(member => ({
            key: member.key.toBase58(),
            permissions: member.permissions
          })),
          threshold: multisigData.threshold,
          transactionIndex: Number(multisigData.transactionIndex.toString()),
          createKey: multisigData.createKey.toBase58(),
          allowExternalExecute: multisigData.allowExternalExecute,
          rentCollector: multisigData.rentCollector?.toBase58(),
          vault: {
            address: vaultPda.toBase58(),
            balance: vaultBalance,
            balanceSOL: vaultBalance / 1e9
          }
        };
      } catch (error) {
        result.multisig = null;
      }
    }

    // 获取Token账户余额
    if (tokenAccount) {
      try {
        const tokenAccountPubkey = new PublicKey(tokenAccount);
        const tokenAccountInfo = await connection.getTokenAccountBalance(tokenAccountPubkey);

        result.tokenBalance = {
          balance: parseInt(tokenAccountInfo.value.amount),
          decimals: tokenAccountInfo.value.decimals,
          uiAmount: tokenAccountInfo.value.uiAmount
        };
      } catch (error) {
        // Token账户不存在时返回0余额
        result.tokenBalance = { balance: 0, decimals: 0, uiAmount: 0 };
      }
    }

    ctx.body = result;
  } catch (error) {
    handleError(ctx, error, '获取账户信息失败');
  }
});

// ==================== 转账接口 ====================

// 创建转账交易（合并SOL和Token转账）
router.post('/api/transfer', async (ctx) => {
  try {
    const { type, multisigAddress, recipientAddress, amount, creatorPublicKey, signedTransaction } = ctx.request.body;

    let requiredParams = ['type', 'multisigAddress', 'recipientAddress', 'amount', 'creatorPublicKey', 'signedTransaction'];

    // Token转账需要额外参数
    if (type === 'token') {
      requiredParams.push('tokenMint', 'decimals');
    }

    const error = validateParams(ctx.request.body, requiredParams);
    if (error) return handleError(ctx, { status: 400 }, error);

    const signature = await sendTransaction(signedTransaction);

    if (type === 'sol') {
      const confirmation = await confirmTransactionWithTimeout(signature);

      if (confirmation.confirmed) {
        ctx.body = {
          signature,
          result: confirmation.result,
          success: true,
          message: `SOL转账成功: ${amount} SOL to ${recipientAddress.substring(0, 8)}...`
        };
      } else {
        ctx.body = {
          signature,
          success: true,
          confirmed: false,
          message: `SOL转账已发送: ${amount} SOL to ${recipientAddress.substring(0, 8)}...`,
          note: '交易确认超时，请稍后检查交易状态',
          explorerUrl: `https://explorer.solana.com/tx/${signature}?cluster=mainnet-beta`
        };
      }
    } else {
      const result = await connection.confirmTransaction(signature);
      ctx.body = {
        signature,
        result,
        success: true,
        message: `Token转账成功: ${amount} Token to ${recipientAddress.substring(0, 8)}...`
      };
    }
  } catch (error) {
    let errorMessage = `创建${ctx.request.body.type === 'sol' ? 'SOL' : 'Token'}转账失败`;
    if (error.message?.includes('insufficient funds')) {
      errorMessage = '账户余额不足';
    } else if (error.message?.includes('Transaction simulation failed')) {
      errorMessage = '交易模拟失败，请检查参数';
    } else if (error.message?.includes('Blockhash not found')) {
      errorMessage = '区块哈希过期，请重新发起交易';
    }
    handleError(ctx, error, errorMessage);
  }
});

// ==================== 交易管理接口 ====================

// 获取交易列表
router.post('/api/transactions', async (ctx) => {
  try {
    const error = validateParams(ctx.request.body, ['multisigAddress']);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { multisigAddress } = ctx.request.body;
    const multisigPubkey = new PublicKey(multisigAddress);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

    const transactions = [];
    const currentTransactionIndex = Number(multisigData.transactionIndex.toString());
    const startIndex = Math.max(1, currentTransactionIndex - MAX_PROPOSALS_TO_CHECK);

    for (let i = startIndex; i <= currentTransactionIndex; i++) {
      try {
        const [proposalPda] = multisig.getProposalPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });

        const proposalAccount = await connection.getAccountInfo(proposalPda);
        if (!proposalAccount) continue;

        const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];

        // 获取交易备注
        const [transactionPda] = multisig.getTransactionPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });

        const transactionAccount = await connection.getAccountInfo(transactionPda);
        let memo = '';
        if (transactionAccount) {
          try {
            const transactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
            memo = transactionData.memo || '';
          } catch (err) {
            // 忽略解析错误
          }
        }

        const approvals = proposalData.approved.length;
        const threshold = multisigData.threshold;

        let status = 'Active';
        if (proposalData.status.__kind === 'Executed') status = 'Executed';
        else if (proposalData.status.__kind === 'Rejected') status = 'Rejected';
        else if (proposalData.status.__kind === 'Cancelled') status = 'Cancelled';
        else if (approvals >= threshold) status = 'Approved';

        const votes = [
          ...proposalData.approved.map(member => ({ member: member.toBase58(), vote: 'Approve' })),
          ...proposalData.rejected.map(member => ({ member: member.toBase58(), vote: 'Reject' }))
        ];

        transactions.push({
          transactionIndex: i,
          status,
          approvals,
          threshold,
          creator: proposalData.creator ? proposalData.creator.toBase58() : 'Unknown',
          memo,
          votes,
          canExecute: status === 'Approved'
        });
      } catch (err) {
        // 静默跳过解析失败的交易
      }
    }

    transactions.sort((a, b) => b.transactionIndex - a.transactionIndex);
    ctx.body = { transactions };
  } catch (error) {
    handleError(ctx, error, '获取交易列表失败');
  }
});

// 构建执行指令（必须在动态路由之前）
router.post('/api/transactions/build-execute', async (ctx) => {
  try {
    const requiredParams = ['multisigAddress', 'transactionIndex', 'executorPublicKey'];
    const error = validateParams(ctx.request.body, requiredParams);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { multisigAddress, transactionIndex, executorPublicKey } = ctx.request.body;
    const multisigPda = new PublicKey(multisigAddress);
    const executorPubkey = new PublicKey(executorPublicKey);

    const instructionResult = await multisig.instructions.vaultTransactionExecute({
      connection,
      multisigPda,
      transactionIndex: BigInt(transactionIndex),
      member: executorPubkey,
      programId: MULTISIG_PROGRAM_ID,
    });

    if (!instructionResult) throw new Error('指令构建返回空结果');

    let instruction;
    if (instructionResult.keys && instructionResult.programId && instructionResult.data !== undefined) {
      instruction = instructionResult;
    } else if (instructionResult.instruction) {
      instruction = instructionResult.instruction;
    } else if (Array.isArray(instructionResult) && instructionResult.length > 0) {
      instruction = instructionResult[0];
    } else {
      throw new Error('无法解析指令结果格式');
    }

    if (!instruction.keys || !instruction.programId || instruction.data === undefined) {
      throw new Error('指令格式不完整');
    }

    ctx.body = {
      instruction: {
        keys: instruction.keys.map(key => ({
          pubkey: key.pubkey.toBase58(),
          isSigner: key.isSigner,
          isWritable: key.isWritable,
        })),
        programId: instruction.programId.toBase58(),
        data: Array.from(instruction.data)
      },
      lookupTableAccounts: []
    };
  } catch (error) {
    handleError(ctx, error, '构建执行指令失败');
  }
});

// 交易操作（投票和执行）
router.post('/api/transactions/:action', async (ctx) => {
  try {
    const { action } = ctx.params;

    if (!['vote', 'execute'].includes(action)) {
      return handleError(ctx, { status: 400 }, '无效的操作类型');
    }

    let requiredParams = ['multisigAddress', 'transactionIndex', 'userPublicKey', 'signedTransaction'];

    if (action === 'vote') {
      requiredParams.push('vote');
    }

    const error = validateParams(ctx.request.body, requiredParams);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { multisigAddress, transactionIndex, signedTransaction } = ctx.request.body;

    if (action === 'execute') {
      // 执行前验证
      const multisigPubkey = new PublicKey(multisigAddress);

      const [proposalPda] = multisig.getProposalPda({
        multisigPda: multisigPubkey,
        transactionIndex: BigInt(transactionIndex),
        programId: MULTISIG_PROGRAM_ID
      });

      const proposalAccount = await connection.getAccountInfo(proposalPda);
      if (!proposalAccount) {
        return handleError(ctx, { status: 400 }, `交易 #${transactionIndex} 不存在`);
      }

      const [transactionPda] = multisig.getTransactionPda({
        multisigPda: multisigPubkey,
        index: BigInt(transactionIndex),
        programId: MULTISIG_PROGRAM_ID
      });

      const transactionAccount = await connection.getAccountInfo(transactionPda);
      if (!transactionAccount) {
        return handleError(ctx, { status: 400 }, `交易 #${transactionIndex} 没有对应的交易内容，无法执行`);
      }

      const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
      const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

      const approvals = proposalData.approved.length;
      const threshold = multisigData.threshold;

      if (approvals < threshold) {
        return handleError(ctx, { status: 400 }, `交易投票不足，需要 ${threshold} 票，当前只有 ${approvals} 票`);
      }

      if (proposalData.status.__kind === 'Executed') {
        return handleError(ctx, { status: 400 }, `交易 #${transactionIndex} 已经执行过了`);
      }

      if (proposalData.status.__kind === 'Cancelled') {
        return handleError(ctx, { status: 400 }, `交易 #${transactionIndex} 已被取消`);
      }

      const executorPubkey = new PublicKey(ctx.request.body.userPublicKey);
      const isMember = multisigData.members.some(member => member.key.equals(executorPubkey));
      if (!isMember) {
        return handleError(ctx, { status: 400 }, '执行者不是多签成员');
      }
    }

    const signature = await sendTransaction(signedTransaction);
    const result = await connection.confirmTransaction(signature);

    ctx.body = {
      signature,
      result,
      success: true,
      message: action === 'vote' ? '投票提交成功' : '交易执行成功'
    };
  } catch (error) {
    const actionName = ctx.params.action === 'vote' ? '投票' : '执行交易';
    handleError(ctx, error, `${actionName}失败`);
  }
});

module.exports = router;