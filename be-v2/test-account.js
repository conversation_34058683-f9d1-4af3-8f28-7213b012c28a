const { getMultisigAccountsForPublicKey, connection } = require('./utils');
const { PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');

// 测试公钥
const TEST_PUBLIC_KEY = 'EMXYq4vz4QEyxj63gerfCGjxGBrBFc7gfoiNdpf85RiG';
// 已知的多签地址
const KNOWN_MULTISIG = 'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr';

async function testGetMultisigAccounts() {
  console.log(`开始测试获取公钥 ${TEST_PUBLIC_KEY} 的多签账户...`);

  try {
    // 直接调用函数测试
    console.log('1. 直接调用 getMultisigAccountsForPublicKey 函数:');
    const multisigs = await getMultisigAccountsForPublicKey(TEST_PUBLIC_KEY);
    console.log(`找到 ${multisigs.length} 个相关多签账户:`);

    if (multisigs.length > 0) {
      multisigs.forEach((ms, index) => {
        console.log(`\n多签账户 #${index + 1}:`);
        console.log(`地址: ${ms.address}`);
        console.log(`成员数: ${ms.members}`);
        console.log(`阈值: ${ms.threshold}`);
        console.log(`交易索引: ${ms.transactionIndex}`);
      });
    } else {
      console.log('未找到相关多签账户');
    }

    // 模拟API调用
    console.log('\n2. 模拟 /api/account 接口调用:');
    const result = {};

    try {
      const multisigs = await getMultisigAccountsForPublicKey(TEST_PUBLIC_KEY);
      result.multisigs = multisigs;

      // 获取SOL余额
      try {
        const userPublicKey = new PublicKey(TEST_PUBLIC_KEY);
        const balance = await connection.getBalance(userPublicKey);
        result.balance = balance;
        console.log(`SOL余额: ${balance / 1e9} SOL`);
      } catch (err) {
        result.balance = 0;
        console.log('获取SOL余额失败');
      }

      console.log(`找到 ${result.multisigs.length} 个相关多签账户`);
    } catch (error) {
      result.multisigs = [];
      result.balance = 0;
      console.log('获取多签账户失败:', error.message);
    }

    // 测试直接获取特定多签账户信息
    console.log('\n3. 直接获取特定多签账户信息:');
    try {
      const multisigPubkey = new PublicKey(KNOWN_MULTISIG);
      const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

      console.log(`多签地址: ${KNOWN_MULTISIG}`);
      console.log(`成员数: ${multisigData.members.length}`);
      console.log(`阈值: ${multisigData.threshold}`);
      console.log(`交易索引: ${Number(multisigData.transactionIndex.toString())}`);

      console.log('\n成员列表:');
      multisigData.members.forEach((member, i) => {
        console.log(`成员 #${i+1}: ${member.key.toBase58()}`);
        console.log(`  是否为测试公钥: ${member.key.equals(new PublicKey(TEST_PUBLIC_KEY))}`);
      });

      // 检查测试公钥是否是成员
      const isMember = multisigData.members.some(member =>
        member.key.equals(new PublicKey(TEST_PUBLIC_KEY))
      );
      console.log(`\n${TEST_PUBLIC_KEY} 是否为多签成员: ${isMember}`);

    } catch (error) {
      console.error('获取特定多签账户信息失败:', error);
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 执行测试
testGetMultisigAccounts()
  .then(() => console.log('测试完成'))
  .catch(err => console.error('测试失败:', err));